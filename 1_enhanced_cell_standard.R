# Enhanced R Script for Cell Journal Publication Standards
# TRMT10C Pan-Cancer Expression Analysis
# Author: [Your Name]
# Date: 2025-07-27

# Load required libraries
library(stringr)
library(dplyr)
library(ggplot2)
library(RColorBrewer)
library(data.table)
library(tibble)
library(ggpubr)
library(scales)
library(cowplot)
library(viridis)
library(ggsignif)
library(tidyr)  # For pivot_wider/pivot_longer functions

#################### ====== Data Processing (Preserved from Original) ====== ####################

# Step 1: Clean GTEx phenotype data
gtex <- read.table("samplepair.txt", header = T, sep = '\t')
tcga_ref <- gtex[, 1:2]
gtex$type <- paste0(gtex$TCGA, "_normal_GTEX")
gtex$sample_type <- "normal"
gtex <- gtex[, c("TCGA", "GTEx", "type", "sample_type")]
names(gtex)[1:2] <- c("tissue", "X_primary_site")

gp <- read.delim(file = "GTEX_phenotype.gz", header = T, as.is = T)
gtex2tcga <- merge(gtex, gp, by = "X_primary_site")
gtex_data <- gtex2tcga[, c(5, 2:4)]
names(gtex_data)[1] <- "sample"

# Step 2: Clean TCGA phenotype data
tcga <- read.delim(file = "TCGA_phenotype_denseDataOnlyDownload.tsv.gz", header = T, as.is = T)
tcga <- merge(tcga_ref, tcga, by.y = "X_primary_disease", by.x = "Detail", all.y = T)
tcga <- tcga[tcga$sample_type %in% c("Primary Tumor", "Solid Tissue Normal"), ]

tcga$type <- ifelse(tcga$sample_type == 'Solid Tissue Normal',
                    paste(tcga$TCGA, "normal_TCGA", sep = "_"), 
                    paste(tcga$TCGA, "tumor_TCGA", sep = "_"))
tcga$sample_type <- ifelse(tcga$sample_type == 'Solid Tissue Normal', "normal", "tumor")
tcga <- tcga[, c(3, 2, 6, 5)]
names(tcga)[2] <- "tissue"

# Combine TCGA and GTEx data
tcga_gtex <- rbind(tcga, gtex_data)
write.table(tcga_gtex, "tcga_gtex_sample.txt", row.names = F, quote = F, sep = "\t")

# Load expression data
exp <- read.table("denseDataOnlyDownload.tsv", header = T, sep = '\t')
exp <- exp[c(1, 3)]  # Extract TPM data only
exp <- merge(exp, tcga_gtex, by = "sample")
colnames(exp)[c(2, 3, 5)] <- c("Gene", "Tissue", "Group")
exp <- exp %>% filter(Gene != -9.966)

# Filter out tissues that don't have both normal and tumor samples
tissue_groups <- exp %>%
  group_by(Tissue) %>%
  summarise(
    has_normal = any(Group == "normal"),
    has_tumor = any(Group == "tumor"),
    .groups = 'drop'
  ) %>%
  filter(has_normal & has_tumor)

# Keep only tissues with both normal and tumor samples
exp_filtered <- exp %>%
  filter(Tissue %in% tissue_groups$Tissue)

cat("Original tissues:", length(unique(exp$Tissue)), "\n")
cat("Tissues with both normal and tumor:", length(unique(exp_filtered$Tissue)), "\n")
cat("Removed tissues:", setdiff(unique(exp$Tissue), unique(exp_filtered$Tissue)), "\n")

# Update exp to use filtered data
exp <- exp_filtered

#################### ====== Enhanced Visualization for Cell Standards ====== ####################

# Define publication-quality parameters
GENE_NAME <- "TRMT10C"
FIGURE_WIDTH <- 16
FIGURE_HEIGHT <- 8
DPI_PUBLICATION <- 600
BASE_FONT_SIZE <- 12

# Cell journal color palette (colorblind-friendly)
cell_palette <- c(
  "normal" = "#2E86AB",  # Professional blue
  "tumor" = "#A23B72"    # Professional magenta
)

# Calculate comprehensive statistics
stats_summary <- exp %>%
  group_by(Tissue, Group) %>%
  summarise(
    n = n(),
    mean_expr = mean(Gene, na.rm = TRUE),
    median_expr = median(Gene, na.rm = TRUE),
    sd_expr = sd(Gene, na.rm = TRUE),
    se_expr = sd_expr / sqrt(n),
    .groups = 'drop'
  ) %>%
  mutate(
    sample_label = paste0("n=", n),
    mean_label = paste0("μ=", round(mean_expr, 2))
  )

# Statistical testing with multiple correction
statistical_tests <- compare_means(
  Gene ~ Group, 
  group.by = "Tissue", 
  data = exp,
  method = "wilcox.test",  # More robust than t-test
  p.adjust.method = "fdr",  # FDR correction
  symnum.args = list(
    cutpoints = c(0, 0.001, 0.01, 0.05, 1), 
    symbols = c("***", "**", "*", "ns")
  )
)

# Calculate effect sizes (Cohen's d)
effect_sizes <- exp %>%
  group_by(Tissue) %>%
  summarise(
    cohens_d = if(length(unique(Group)) == 2) {
      normal_vals <- Gene[Group == "normal"]
      tumor_vals <- Gene[Group == "tumor"]
      if(length(normal_vals) > 0 & length(tumor_vals) > 0) {
        pooled_sd <- sqrt(((length(normal_vals) - 1) * var(normal_vals) + 
                          (length(tumor_vals) - 1) * var(tumor_vals)) / 
                         (length(normal_vals) + length(tumor_vals) - 2))
        (mean(tumor_vals) - mean(normal_vals)) / pooled_sd
      } else NA
    } else NA,
    .groups = 'drop'
  )

# Determine optimal y-axis range
y_range <- range(exp$Gene, na.rm = TRUE)
y_buffer <- diff(y_range) * 0.15
y_limits <- c(y_range[1] - y_buffer, y_range[2] + y_buffer)

# Create the main publication-quality plot
main_plot <- ggplot(exp, aes(x = Tissue, y = Gene, fill = Group, color = Group)) +
  
  # Enhanced box plots
  geom_boxplot(
    alpha = 0.7,
    outlier.size = 1,
    outlier.alpha = 0.6,
    width = 0.6,
    position = position_dodge(0.8),
    size = 0.6
  ) +
  
  # Add individual points (optional, for transparency)
  # geom_jitter(
  #   position = position_jitterdodge(dodge.width = 0.8, jitter.width = 0.2),
  #   alpha = 0.3,
  #   size = 0.5
  # ) +
  
  # Color scheme
  scale_fill_manual(values = cell_palette, name = "Sample Type") +
  scale_color_manual(values = cell_palette, name = "Sample Type") +
  
  # Sample size annotations
  geom_text(
    data = stats_summary,
    aes(label = sample_label, y = y_limits[1] + 0.05 * diff(y_limits), color = Group),
    position = position_dodge(0.8),
    size = 3,
    hjust = 0.5,
    show.legend = FALSE,
    fontface = "bold"
  ) +
  
  # Statistical significance
  stat_pvalue_manual(
    statistical_tests,
    x = "Tissue",
    y.position = y_limits[2] - 0.08 * diff(y_limits),
    label = "p.signif",
    position = position_dodge(0.8),
    size = 4,
    bracket.size = 0.5,
    tip.length = 0.02
  ) +
  
  # Labels and title
  labs(
    x = "Cancer Type",
    y = paste0(GENE_NAME, " Expression (log₂(TPM+1))"),
    title = paste0(GENE_NAME, " Expression Across Cancer Types"),
    subtitle = "Tumor vs. Normal Tissue Comparison (TCGA & GTEx)"
  ) +
  
  # Coordinate system
  coord_cartesian(ylim = y_limits) +
  
  # Publication-quality theme
  theme_classic(base_size = BASE_FONT_SIZE) +
  theme(
    # Text elements
    axis.text.x = element_text(
      angle = 45, hjust = 1, vjust = 1,
      size = 11, color = "black", face = "bold"
    ),
    axis.text.y = element_text(size = 11, color = "black"),
    axis.title = element_text(size = 13, color = "black", face = "bold"),
    axis.title.x = element_text(margin = margin(t = 15)),
    axis.title.y = element_text(margin = margin(r = 15)),
    
    # Title elements
    plot.title = element_text(
      size = 16, face = "bold", hjust = 0.5,
      margin = margin(b = 5)
    ),
    plot.subtitle = element_text(
      size = 12, hjust = 0.5,
      margin = margin(b = 20), color = "gray30"
    ),
    
    # Legend
    legend.position = "top",
    legend.title = element_text(size = 12, face = "bold"),
    legend.text = element_text(size = 11),
    legend.box.margin = margin(b = 15),
    legend.key.size = unit(1.2, "cm"),
    
    # Panel aesthetics
    panel.grid.major.y = element_line(color = "gray90", size = 0.3),
    panel.grid.minor = element_blank(),
    panel.border = element_rect(color = "black", fill = NA, size = 1),
    
    # Margins
    plot.margin = margin(t = 25, r = 25, b = 25, l = 25),
    
    # Background
    plot.background = element_rect(fill = "white", color = NA),
    panel.background = element_rect(fill = "white", color = NA)
  )

# Display the plot
print(main_plot)

# Create output directory
if (!dir.exists("figure")) {
  dir.create("figure", recursive = TRUE)
}

# Save publication-quality figures
base_filename <- paste0("figure/", GENE_NAME, "_pancancer_Cell_publication")

# PDF (vector format, preferred by most journals)
ggsave(
  paste0(base_filename, ".pdf"),
  plot = main_plot,
  width = FIGURE_WIDTH, height = FIGURE_HEIGHT,
  units = "in", dpi = DPI_PUBLICATION,
  device = cairo_pdf
)

# High-resolution TIFF (required by many journals)
ggsave(
  paste0(base_filename, ".tiff"),
  plot = main_plot,
  width = FIGURE_WIDTH, height = FIGURE_HEIGHT,
  units = "in", dpi = DPI_PUBLICATION,
  compression = "lzw"
)

# PNG for presentations/web
ggsave(
  paste0(base_filename, ".png"),
  plot = main_plot,
  width = FIGURE_WIDTH, height = FIGURE_HEIGHT,
  units = "in", dpi = DPI_PUBLICATION,
  type = "cairo"
)

# Export summary tables
write.csv(stats_summary, "figure/expression_summary_statistics.csv", row.names = FALSE)
write.csv(statistical_tests, "figure/statistical_test_results.csv", row.names = FALSE)
write.csv(effect_sizes, "figure/effect_sizes.csv", row.names = FALSE)

#################### ====== Generate Prism-Compatible Data ====== ####################

# Create Prism-compatible data format
# Method 1: Wide format with separate columns for each tissue type
prism_wide <- exp %>%
  select(Gene, Tissue, Group) %>%
  unite("Tissue_Group", Tissue, Group, sep = "_") %>%
  group_by(Tissue_Group) %>%
  mutate(row_id = row_number()) %>%
  pivot_wider(names_from = Tissue_Group, values_from = Gene) %>%
  select(-row_id)

# Method 2: Separate sheets for normal and tumor (more organized for Prism)
prism_normal <- exp %>%
  filter(Group == "normal") %>%
  select(Gene, Tissue) %>%
  group_by(Tissue) %>%
  mutate(row_id = row_number()) %>%
  pivot_wider(names_from = Tissue, values_from = Gene) %>%
  select(-row_id)

prism_tumor <- exp %>%
  filter(Group == "tumor") %>%
  select(Gene, Tissue) %>%
  group_by(Tissue) %>%
  mutate(row_id = row_number()) %>%
  pivot_wider(names_from = Tissue, values_from = Gene) %>%
  select(-row_id)

# Method 3: Long format with clear grouping (easiest for Prism import)
prism_long <- exp %>%
  select(Gene, Tissue, Group) %>%
  arrange(Tissue, Group)

# Method 4: Prism-optimized format (side-by-side columns for each tissue)
prism_optimized <- exp %>%
  select(Gene, Tissue, Group) %>%
  group_by(Tissue, Group) %>%
  mutate(row_id = row_number()) %>%
  ungroup() %>%
  pivot_wider(
    names_from = c(Tissue, Group),
    values_from = Gene,
    names_sep = "_"
  ) %>%
  select(-row_id)

# Reorder columns to have normal and tumor side by side for each tissue
tissue_names <- unique(exp$Tissue)
col_order <- c()
for(tissue in tissue_names) {
  col_order <- c(col_order, paste0(tissue, "_normal"), paste0(tissue, "_tumor"))
}
# Keep only columns that exist
col_order <- col_order[col_order %in% names(prism_optimized)]
prism_optimized <- prism_optimized[, col_order]

# Save Prism-compatible files
write.csv(prism_wide, "figure/prism_data_wide_format.csv", row.names = FALSE, na = "")
write.csv(prism_normal, "figure/prism_data_normal_only.csv", row.names = FALSE, na = "")
write.csv(prism_tumor, "figure/prism_data_tumor_only.csv", row.names = FALSE, na = "")
write.csv(prism_long, "figure/prism_data_long_format.csv", row.names = FALSE)
write.csv(prism_optimized, "figure/prism_data_BEST_for_import.csv", row.names = FALSE, na = "")

# Create Excel file with multiple sheets for Prism (requires openxlsx package)
if (require(openxlsx, quietly = TRUE)) {
  wb <- createWorkbook()

  # Add worksheets
  addWorksheet(wb, "Normal_Samples")
  addWorksheet(wb, "Tumor_Samples")
  addWorksheet(wb, "Combined_Long_Format")
  addWorksheet(wb, "Summary_Statistics")

  # Write data to worksheets
  writeData(wb, "Normal_Samples", prism_normal)
  writeData(wb, "Tumor_Samples", prism_tumor)
  writeData(wb, "Combined_Long_Format", prism_long)
  writeData(wb, "Summary_Statistics", stats_summary)

  # Save Excel file
  saveWorkbook(wb, "figure/prism_data_complete.xlsx", overwrite = TRUE)

  cat("Excel file with multiple sheets created: figure/prism_data_complete.xlsx\n")
} else {
  cat("Note: Install 'openxlsx' package to generate Excel file with multiple sheets\n")
  cat("Run: install.packages('openxlsx')\n")
}

# Create a summary of available tissues and sample sizes for Prism reference
prism_summary <- exp %>%
  group_by(Tissue, Group) %>%
  summarise(
    Sample_Count = n(),
    Mean_Expression = round(mean(Gene, na.rm = TRUE), 3),
    SD_Expression = round(sd(Gene, na.rm = TRUE), 3),
    .groups = 'drop'
  ) %>%
  pivot_wider(
    names_from = Group,
    values_from = c(Sample_Count, Mean_Expression, SD_Expression),
    names_sep = "_"
  )

write.csv(prism_summary, "figure/prism_tissue_summary.csv", row.names = FALSE)

# Print comprehensive summary
cat("\n", rep("=", 70), "\n", sep = "")
cat("PUBLICATION-READY ANALYSIS COMPLETED\n")
cat(rep("=", 70), "\n", sep = "")
cat("\n📊 FILTERED DATA:\n")
cat("- Total tissues analyzed:", length(unique(exp$Tissue)), "\n")
cat("- Total samples:", nrow(exp), "\n")
cat("- Normal samples:", sum(exp$Group == "normal"), "\n")
cat("- Tumor samples:", sum(exp$Group == "tumor"), "\n")

cat("\n📈 PUBLICATION FIGURES:\n")
cat("- PDF:", paste0(base_filename, ".pdf\n"))
cat("- TIFF:", paste0(base_filename, ".tiff\n"))
cat("- PNG:", paste0(base_filename, ".png\n"))

cat("\n📋 STATISTICAL ANALYSIS:\n")
cat("- Summary statistics: figure/expression_summary_statistics.csv\n")
cat("- Statistical tests: figure/statistical_test_results.csv\n")
cat("- Effect sizes: figure/effect_sizes.csv\n")

cat("\n🔬 PRISM-COMPATIBLE DATA:\n")
cat("- ⭐ BEST for Prism: figure/prism_data_BEST_for_import.csv\n")
cat("- Wide format: figure/prism_data_wide_format.csv\n")
cat("- Normal samples only: figure/prism_data_normal_only.csv\n")
cat("- Tumor samples only: figure/prism_data_tumor_only.csv\n")
cat("- Long format: figure/prism_data_long_format.csv\n")
cat("- Tissue summary: figure/prism_tissue_summary.csv\n")
if (require(openxlsx, quietly = TRUE)) {
  cat("- Excel workbook: figure/prism_data_complete.xlsx\n")
}

cat("\n💡 PRISM IMPORT INSTRUCTIONS:\n")
cat("🎯 RECOMMENDED: Use 'prism_data_BEST_for_import.csv'\n")
cat("   - Data in: Columns ✅\n")
cat("   - Each tissue has normal_tumor columns side by side\n")
cat("   - Perfect for grouped box plots\n")
cat("\n📋 Alternative options:\n")
cat("1. Long format: 'prism_data_long_format.csv' (Data in: Rows)\n")
cat("2. Separate files: normal_only.csv + tumor_only.csv\n")
cat("3. Excel workbook: prism_data_complete.xlsx\n")

cat("\n", rep("=", 70), "\n", sep = "")
