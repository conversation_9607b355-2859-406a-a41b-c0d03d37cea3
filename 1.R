library(stringr)
library(dplyr)
library(ggplot2)
library(RColorBrewer)
library(data.table)

#################### ====== step1: clean GTEx pheno data ====== ####################
gtex <- read.table("samplepair.txt",header=T,sep='\t')
tcga_ref <- gtex[,1:2]
gtex$type <- paste0(gtex$TCGA,"_normal_GTEX")
gtex$sample_type <- "normal"
gtex <- gtex[,c("TCGA","GTEx","type","sample_type")]
names(gtex)[1:2] <- c("tissue","X_primary_site")

gp <- read.delim(file="GTEX_phenotype.gz",header=T,as.is = T)
gtex2tcga <- merge(gtex,gp,by="X_primary_site")
gtex_data <- gtex2tcga[,c(5,2:4)]
names(gtex_data)[1] <- "sample"
#write.table(gtex_data,"GTEX_pheno.txt",row.names=F,quote=F,sep='\t')



#################### ====== step2: clean TCGA pheno data ====== ####################
tcga <- read.delim(file="TCGA_phenotype_denseDataOnlyDownload.tsv.gz",header=T,as.is = T)
tcga <- merge(tcga_ref,tcga,by.y="X_primary_disease",by.x="Detail",all.y = T)
tcga <- tcga[tcga$sample_type %in% c("Primary Tumor","Solid Tissue Normal"),]

tcga$type <- ifelse(tcga$sample_type=='Solid Tissue Normal',
                    paste(tcga$TCGA,"normal_TCGA",sep="_"),paste(tcga$TCGA,"tumor_TCGA",sep="_"))
tcga$sample_type <- ifelse(tcga$sample_type=='Solid Tissue Normal',"normal","tumor")
tcga <- tcga[,c(3,2,6,5)]
names(tcga)[2] <- "tissue"
#write.table(tcga,"tcga_pheno.txt",row.names = F,quote=F,sep='\t')
tcga_gtex <- rbind(tcga,gtex_data)
write.table(tcga_gtex,"tcga_gtex_sample.txt",row.names = F,quote = F,sep = "\t")
#保存为TXT文件之后，下次再用可以直接读取该TXT文件，前面就不用再运行了

#该 "abc.tsv" 文件是从xena网站下载的
exp <- read.table("denseDataOnlyDownload.tsv",header=T,sep='\t')
exp <- exp[c(1,3)]#仅提取TPM数据
exp <- merge(exp,tcga_gtex,by="sample")
colnames(exp)[c(2,3,5)] <- c("Gene","Tissue","Group")
exp <- exp %>% plotly::filter(Gene!=-9.966)


# ==================== Enhanced Visualization for Cell Journal Standards ====================
# Load additional packages for enhanced visualization
library(stringr)
library(dplyr)
library(ggplot2)
library(RColorBrewer)
library(data.table)
library(tibble)
library(ggpubr)
library(scales)
library(cowplot)
library(viridis)

# Define Cell journal-compliant color palette
cell_colors <- c("normal" = "#2E86AB", "tumor" = "#A23B72")

# Calculate sample sizes for each group
count_N <- exp %>%
  group_by(Tissue, Group) %>%
  summarise(n = n(), .groups = 'drop') %>%
  mutate(label = paste0("n=", n))

# Calculate statistical comparisons
comp <- compare_means(Gene ~ Group, group.by = "Tissue", data = exp,
                      method = "t.test",
                      symnum.args = list(cutpoints = c(0, 0.001, 0.01, 0.05, 1),
                                       symbols = c("***", "**", "*", "ns")),
                      p.adjust.method = "holm")

# Calculate y-axis limits for better visualization
y_max <- max(exp$Gene, na.rm = TRUE)
y_min <- min(exp$Gene, na.rm = TRUE)
y_range <- y_max - y_min
y_upper <- y_max + 0.15 * y_range
y_lower <- y_min - 0.1 * y_range

# Create the main plot with Cell journal standards
p_main <- ggplot(exp, aes(x = Tissue, y = Gene, fill = Group)) +
  # Box plots with enhanced aesthetics
  geom_boxplot(aes(color = Group),
               alpha = 0.8,
               outlier.size = 0.8,
               outlier.alpha = 0.6,
               width = 0.7,
               position = position_dodge(0.8)) +

  # Color scheme
  scale_fill_manual(values = cell_colors, name = "Sample Type") +
  scale_color_manual(values = cell_colors, name = "Sample Type") +

  # Add sample size annotations
  geom_text(data = count_N,
            aes(label = label, y = y_lower + 0.05 * y_range, color = Group),
            position = position_dodge(0.8),
            size = 2.8,
            angle = 0,
            hjust = 0.5,
            show.legend = FALSE) +

  # Statistical significance annotations
  stat_pvalue_manual(comp,
                     x = "Tissue",
                     y.position = y_upper - 0.05 * y_range,
                     label = "p.signif",
                     position = position_dodge(0.8),
                     size = 3.5,
                     bracket.size = 0.4) +

  # Axis labels and title
  labs(x = "Cancer Type",
       y = "TRMT10C Expression (log2(TPM+1))",
       title = "TRMT10C Expression Across Cancer Types",
       subtitle = "Comparison between tumor and normal tissues") +

  # Y-axis limits
  coord_cartesian(ylim = c(y_lower, y_upper)) +

  # Enhanced theme for Cell journal
  theme_classic(base_size = 12) +
  theme(
    # Axis text and labels
    axis.text.x = element_text(angle = 45, hjust = 1, vjust = 1,
                               size = 10, color = "black"),
    axis.text.y = element_text(size = 10, color = "black"),
    axis.title = element_text(size = 12, color = "black", face = "bold"),

    # Title and subtitle
    plot.title = element_text(size = 14, face = "bold", hjust = 0.5,
                              margin = margin(b = 5)),
    plot.subtitle = element_text(size = 11, hjust = 0.5,
                                 margin = margin(b = 15), color = "gray30"),

    # Legend
    legend.position = "top",
    legend.title = element_text(size = 11, face = "bold"),
    legend.text = element_text(size = 10),
    legend.box.margin = margin(b = 10),

    # Panel and grid
    panel.grid.major.y = element_line(color = "gray90", size = 0.3),
    panel.grid.minor = element_blank(),
    panel.border = element_rect(color = "black", fill = NA, size = 0.8),

    # Plot margins
    plot.margin = margin(t = 20, r = 20, b = 20, l = 20),

    # Strip text for facets (if needed)
    strip.background = element_rect(fill = "gray95", color = "black"),
    strip.text = element_text(size = 10, face = "bold")
  )

# Display the plot
print(p_main)

# Create output directory if it doesn't exist
if (!dir.exists("figure")) {
  dir.create("figure", recursive = TRUE)
}

# Save high-quality versions for publication
# PDF version (vector graphics, preferred for journals)
ggsave("figure/TRMT10C_pancancer_Cell_standard.pdf",
       plot = p_main,
       width = 16, height = 8,
       units = "in",
       dpi = 300,
       device = cairo_pdf)

# High-resolution PNG version
ggsave("figure/TRMT10C_pancancer_Cell_standard.png",
       plot = p_main,
       width = 16, height = 8,
       units = "in",
       dpi = 600,
       type = "cairo")

# TIFF version (often required by journals)
ggsave("figure/TRMT10C_pancancer_Cell_standard.tiff",
       plot = p_main,
       width = 16, height = 8,
       units = "in",
       dpi = 600,
       compression = "lzw")

# Print summary statistics
cat("\n=== Summary Statistics ===\n")
print(count_N)
cat("\n=== Statistical Test Results ===\n")
print(comp[, c("group1", "group2", ".y.", "p", "p.adj", "p.signif")])

cat("\n=== Plot saved in multiple formats ===\n")
cat("- PDF: figure/TRMT10C_pancancer_Cell_standard.pdf\n")
cat("- PNG: figure/TRMT10C_pancancer_Cell_standard.png\n")
cat("- TIFF: figure/TRMT10C_pancancer_Cell_standard.tiff\n")