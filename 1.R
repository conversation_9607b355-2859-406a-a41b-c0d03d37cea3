library(stringr)
library(dplyr)
library(ggplot2)
library(RColorBrewer)
library(data.table)

#################### ====== step1: clean GTEx pheno data ====== ####################
gtex <- read.table("samplepair.txt",header=T,sep='\t')
tcga_ref <- gtex[,1:2]
gtex$type <- paste0(gtex$TCGA,"_normal_GTEX")
gtex$sample_type <- "normal"
gtex <- gtex[,c("TCGA","GTEx","type","sample_type")]
names(gtex)[1:2] <- c("tissue","X_primary_site")

gp <- read.delim(file="GTEX_phenotype.gz",header=T,as.is = T)
gtex2tcga <- merge(gtex,gp,by="X_primary_site")
gtex_data <- gtex2tcga[,c(5,2:4)]
names(gtex_data)[1] <- "sample"
#write.table(gtex_data,"GTEX_pheno.txt",row.names=F,quote=F,sep='\t')



#################### ====== step2: clean TCGA pheno data ====== ####################
tcga <- read.delim(file="TCGA_phenotype_denseDataOnlyDownload.tsv.gz",header=T,as.is = T)
tcga <- merge(tcga_ref,tcga,by.y="X_primary_disease",by.x="Detail",all.y = T)
tcga <- tcga[tcga$sample_type %in% c("Primary Tumor","Solid Tissue Normal"),]

tcga$type <- ifelse(tcga$sample_type=='Solid Tissue Normal',
                    paste(tcga$TCGA,"normal_TCGA",sep="_"),paste(tcga$TCGA,"tumor_TCGA",sep="_"))
tcga$sample_type <- ifelse(tcga$sample_type=='Solid Tissue Normal',"normal","tumor")
tcga <- tcga[,c(3,2,6,5)]
names(tcga)[2] <- "tissue"
#write.table(tcga,"tcga_pheno.txt",row.names = F,quote=F,sep='\t')
tcga_gtex <- rbind(tcga,gtex_data)
write.table(tcga_gtex,"tcga_gtex_sample.txt",row.names = F,quote = F,sep = "\t")
#保存为TXT文件之后，下次再用可以直接读取该TXT文件，前面就不用再运行了

#该 "abc.tsv" 文件是从xena网站下载的
exp <- read.table("denseDataOnlyDownload.tsv",header=T,sep='\t')
exp <- exp[c(1,3)]#仅提取TPM数据
exp <- merge(exp,tcga_gtex,by="sample")
colnames(exp)[c(2,3,5)] <- c("Gene","Tissue","Group")
exp <- exp %>% plotly::filter(Gene!=-9.966)


#绘图
library(stringr)
library(dplyr)
library(ggplot2)
library(RColorBrewer)
library(data.table)
library(tibble)
library(ggplot2)
library(ggpubr)
library(RColorBrewer)

ylabname <- paste("TRMT10C", "expression") #Y轴名字
p1 <- ggboxplot(exp, x = "Tissue", y = "Gene", fill = 'Group',
                ylab = ylabname,
                color = "Group",
                palette = c("#00AFBB", "#FC4E07"),
                ggtheme = theme_minimal())



## 计算每种肿瘤正常和肿瘤组织的样本量
count_N <- exp %>% group_by(Tissue, Group) %>% tally
count_N$n <- paste('n = ', count_N$n)
## 添加N = 到图中
p1 <- p1 + geom_text(data=count_N, aes(label=n, y=-9, color=Group), position=position_dodge2(0.9), size = 3, angle=90, hjust = 0) +
  theme(axis.text.x = element_text(angle = 45, hjust = 1.2))

#计算t检验显著性
comp <- compare_means(Gene ~ Group, group.by = "Tissue", data = exp,
                      method = "t.test", symnum.args = list(cutpoints = c(0,0.001, 0.01, 0.05, 1), symbols = c("****", "***", "*", "ns")),
                      p.adjust.method = "holm")
#添加显著性标记
p2 <- p1 + stat_pvalue_manual(comp, x = "Tissue", y.position = 7.5,
                              label = "p.signif", position = position_dodge(0.8))

p2
#dev.off()

## 保存图片
### pdf version
ggsave("figure/pancancer_Plot.pdf", width = 14, height = 5)

### png version
#png("figure/pancancer_Plot.png", width = 465, height = 225, units='mm', res = 300)